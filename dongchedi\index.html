<!DOCTYPE html>
<!-- saved from url=(0046)https://shouji.baidu.com/software/7936956.html -->
<html data-dpr="1" style="font-size: 37.52px;">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

		<title>懂车帝APP免费下载安装2023最新版_手机APP下载_百度手机助手</title>
		<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,initial-scale=1.0,user-scalable=0">
		<link rel="icon" href="https://ascdn.baidu.com/appsite/images/logo57x57.png" sizes="128x128">
		<meta name="keywords" content="懂车帝，懂车帝下载安装，懂车帝下载，懂车帝app，懂车帝最新版，懂车帝官网免费下载">
		<meta name="description" content="百度手机助手为您提供懂车帝APP下载安装2023最新版,懂车帝APP是一款专业的汽车资讯和购车服务平台，为用户提供全面的汽车信息、专业的购车指导和丰富的汽车内容，主要功能包括：
- 汽车资讯和评测
- 购车指导和对比
- 汽车视频内容...">
		<meta name="applicable-device" content="mobile">
		<meta name="baidu-site-verification" content="code-VJdGJriRrE">
		<link rel="canonical" href="https://mobile.baidu.com/appitemp/1499329">
		<style type="text/css">
			.agl-hover {
			                outline: 2px solid rgba(244,67,54,0.99) !important;
			                background-color: rgba(244,67,54,0.2) !important;
			                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.8) !important;
			            }
			            .agl-highlight-current {
			                outline: 2px solid rgba(255,87,34,0.99) !important;
			                background-color: rgba(255,87,34,0.4) !important;
			            }
			            .agl-highlight-similar {
			                outline: 2px dashed rgba(255,87,34,0.99) !important;
			                background-color: rgba(255,87,34,0.4) !important;
			            }
			            .agl-highlight-group {
			                outline: 4px double rgba(255,87,34,0.99) !important;
			                background-color: rgba(255,87,34,0.4) !important;
			            }
			            .agl-cover-current {
			                
			                position: absolute !important;
			                top: 0;
			                left: 0;
			                z-index: 9 !important;
			        
			                outline: 2px solid rgba(33,33,33,0.99) !important;
			                background-color: rgba(33,33,33,0.6) !important;
			            }
			            .agl-cover-similar {
			                
			                position: absolute !important;
			                top: 0;
			                left: 0;
			                z-index: 9 !important;
			        
			                outline: 2px dashed rgba(33,33,33,0.99) !important;
			                background-color: rgba(33,33,33,0.6) !important;
			            }
			            .agl-cover-group {
			                
			                position: absolute !important;
			                top: 0;
			                left: 0;
			                z-index: 9 !important;
			        
			                outline: 4px double rgba(33,33,33,0.99) !important;
			                background-color: rgba(33,33,33,0.6) !important;
			            }
			            .agl-cover-focus {
			                outline: 3px solid rgba(255,165,0,0.99) !important;
			                background-color: rgba(255,165,0,0.4) !important;
			            }
		</style>

		<link rel="preload" href="static/common.27bf2501.css" as="style">
		<link rel="stylesheet" href="static/common.27bf2501.css">
		<style>
			/* 图片轮播增强样式 */
			.img-list.detailImage li {
				cursor: pointer;
				transition: transform 0.2s ease;
			}

			.img-list.detailImage li:hover {
				transform: scale(1.05);
			}

			.full-screen.screen {
				z-index: 9999;
				background-color: rgba(0, 0, 0, 0.9);
			}

			.full-screen .swiper .item .img-wrap {
				background-color: transparent;
			}

			.full-screen .swiper .item .img-wrap img {
				max-width: 90%;
				max-height: 90%;
				width: auto;
				height: auto;
				object-fit: contain;
			}

			/* 功能简介样式优化 */
			.spread-button {
				cursor: pointer;
				color: #2681ff;
				font-size: 14px;
				margin-top: 10px;
				user-select: none;
				transition: color 0.2s ease;
			}

			.spread-button:hover {
				color: #1a5bb8;
			}

			.text-wrap {
				position: relative;
			}

			.text-wrap p {
				transition: opacity 0.3s ease;
			}

			/* 下载按钮样式优化 */
			.double-btn-item, .safe-btn, .normal-btn, .direct-btn {
				cursor: pointer;
				transition: opacity 0.2s ease;
			}

			.double-btn-item:hover, .safe-btn:hover, .normal-btn:hover, .direct-btn:hover {
				opacity: 0.9;
			}

			/* 禁用链接样式 */
			a {
				color: inherit !important;
				text-decoration: none !important;
				cursor: default !important;
			}

			/* 轮播指示器样式优化 */
			.swiper-dot {
				cursor: pointer;
				transition: background-color 0.2s ease;
			}

			.swiper-dot:hover {
				background-color: rgba(5, 180, 255, 0.7);
			}

			/* 关闭按钮样式 */
			.close-btn {
				transition: all 0.2s ease;
			}

			.close-btn:hover {
				background: rgba(0,0,0,0.8) !important;
				transform: scale(1.1);
			}
		</style>
		<style data-vue-ssr-id="85bdc7ca:0 ae6bd8e4:0 6f998fd6:0 422a5b69:0 788f32eb:0 c75999ca:0 00c2c14a:0 fc79031c:0 0218e574:0 7037afad:0 2ab7d0c5:0 e24fef14:0 05fbd735:0 35ef622a:0 4d343cc5:0 652459d9:0 5386a32b:0 6c339fdb:0 5a95e92d:0">.item-page[data-v-02a50d9c]{padding-bottom:45px}.app .has-bottom-button[data-v-02a50d9c]{padding-bottom:95px}.is-show-pc[data-v-02a50d9c]{max-width:500px;margin:auto}@media screen and (max-width:359px){.has-bottom-button[data-v-02a50d9c]{padding-bottom:116px}}.button-direct[data-v-02a50d9c]{padding-bottom:49px}.fc-s1-dev-card[data-v-02a50d9c]{background:#f8f8f8;padding-top:12px;padding-bottom:12px}.fixed-page-bottom[data-v-02a50d9c]{position:fixed;bottom:0;z-index:999}.page-center-banner-card[data-v-02a50d9c]{overflow:hidden;margin-bottom:12px;margin-left:17px;margin-right:17px;padding-bottom:0!important;-moz-border-radius:9px;border-radius:9px}.page-center-banner-card[data-v-02a50d9c] .detail-banner-description{-webkit-transform:translate(14%,14%) scale(.72);-moz-transform:translate(14%,14%) scale(.72);-ms-transform:translate(14%,14%) scale(.72);transform:translate(14%,14%) scale(.72)}.fix-no-border[data-v-02a50d9c]{border:0!important}
.detail-banner-wrap[data-v-51515d96]{position:relative;width:100%;padding-bottom:33.25%;background-position:50%;-moz-background-size:contain;background-size:contain;background-repeat:no-repeat;overflow:hidden}.detail-banner-wrap.popup-banner[data-v-51515d96]{padding-bottom:24.15%}.detail-banner-filter[data-v-51515d96]{position:absolute;width:100%;top:55%;height:45%;background-image:-webkit-gradient(linear,left top,left bottom,from(transparent),to(rgba(0,0,0,.5)));background-image:-webkit-linear-gradient(top,transparent,rgba(0,0,0,.5));background-image:-moz- oldlinear-gradient(top,transparent 0,rgba(0,0,0,.5) 100%);background-image:linear-gradient(180deg,transparent,rgba(0,0,0,.5))}.detail-banner-button[data-v-51515d96]{position:absolute;top:50%;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);right:13px;width:100px;height:33px;background-image:-webkit-gradient(linear,left top,left bottom,from(#ffe688),to(#ff9100));background-image:-webkit-linear-gradient(top,#ffe688,#ff9100);background-image:-moz- oldlinear-gradient(top,#ffe688 0,#ff9100 100%);background-image:linear-gradient(180deg,#ffe688,#ff9100);-moz-border-radius:50px;border-radius:50px;font-size:16px;font-weight:600;color:#fff;letter-spacing:0;text-align:center;text-shadow:0 3px 3px #ff9905;overflow:hidden}.detail-banner-progress[data-v-51515d96]{height:100%;background-color:rgba(0,0,0,.1);-webkit-transition:all .5s ease;-moz-transition:all .5s ease;transition:all .5s ease}.detail-banner-text[data-v-51515d96]{position:absolute;top:50%;left:50%;width:100%;height:100%;line-height:33px;-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:100}.detail-banner-description[data-v-51515d96]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;position:absolute;right:13px;bottom:5px;font-size:14px;-webkit-transform:translate(10%,10%) scale(.8);-moz-transform:translate(10%,10%) scale(.8);-ms-transform:translate(10%,10%) scale(.8);transform:translate(10%,10%) scale(.8);font-weight:400;color:hsla(0,0%,100%,.8);white-space:nowrap}.detail-banner-app-name[data-v-51515d96],.detail-banner-developer[data-v-51515d96],.detail-banner-permission[data-v-51515d96],.detail-banner-privacy[data-v-51515d96]{line-height:100%;margin-right:8px;white-space:nowrap}.detail-banner-version[data-v-51515d96]{line-height:100%;white-space:nowrap}.detail-banner-developer[data-v-51515d96]{max-width:155px}.detail-banner-app-name[data-v-51515d96]{max-width:100px}.detail-banner-bottom[data-v-51515d96]{position:absolute;bottom:8px;right:15px;left:15px}.detail-banner-new[data-v-51515d96]{padding:15px 15px 0}.detail-banner-new-inter-wrap[data-v-51515d96],.detail-banner-new-wrap[data-v-51515d96]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;background:#f5f5f5;-moz-border-radius:9px;border-radius:9px;width:100%}.detail-banner-new-left[data-v-51515d96]{position:relative;width:270px}.detail-banner-new-ad[data-v-51515d96]{left:5px;top:5px;position:absolute;width:26px;height:14px;-moz-border-radius:3px;border-radius:3px}.detail-banner-new-bg[data-v-51515d96]{padding-bottom:33.25%;background-position:50%;-moz-background-size:contain;background-size:contain;background-repeat:no-repeat;overflow:hidden;-moz-border-radius:9px 0 0 9px;border-radius:9px 0 0 9px}.detail-banner-new-filter[data-v-51515d96]{position:absolute;width:100%;top:55%;height:45%;-moz-border-radius:0 0 0 9px;border-radius:0 0 0 9px;background-image:-webkit-gradient(linear,left top,left bottom,from(transparent),to(rgba(0,0,0,.5)));background-image:-webkit-linear-gradient(top,transparent,rgba(0,0,0,.5));background-image:-moz- oldlinear-gradient(top,transparent,rgba(0,0,0,.5));background-image:linear-gradient(180deg,transparent,rgba(0,0,0,.5))}.detail-banner-new-bottom[data-v-51515d96]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;margin-top:8px;grid-column-gap:8px}.detail-banner-new-description[data-v-51515d96]{line-height:1;float:left}.detail-banner-new-link-wrap[data-v-51515d96]{color:#fff;line-height:1;float:left}.detail-banner-new-info-left[data-v-51515d96]{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.detail-banner-new-info-left[data-v-51515d96],.detail-banner-new-info-right[data-v-51515d96]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;grid-column-gap:8px;line-height:1;font-size:12px;opacity:.8;color:#858585;overflow:hidden}.detail-banner-new-info-right[data-v-51515d96]{-webkit-box-flex:0;-webkit-flex:none;-moz-box-flex:0;-ms-flex:none;flex:none;white-space:nowrap}.detail-banner-new-developer[data-v-51515d96],.detail-banner-new-permission[data-v-51515d96],.detail-banner-new-privacy[data-v-51515d96]{white-space:nowrap}.detail-banner-new-right[data-v-51515d96]{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;text-align:center;-moz-border-radius:0 9px 9px 0;border-radius:0 9px 9px 0;background-image:-webkit-gradient(linear,right top,left top,from(#f7fbff),color-stop(74%,#f7faff),to(rgba(242,247,255,0)));background-image:-webkit-linear-gradient(right,#f7fbff,#f7faff 74%,rgba(242,247,255,0));background-image:-moz- oldlinear-gradient(right,#f7fbff,#f7faff 74%,rgba(242,247,255,0));background-image:linear-gradient(270deg,#f7fbff,#f7faff 74%,rgba(242,247,255,0))}.detail-banner-new-app-name[data-v-51515d96]{font-size:14px;line-height:14px;font-weight:500;display:block;margin-top:18px;color:#1f1f1f;letter-spacing:0;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;padding:0 7px}.detail-banner-new-version[data-v-51515d96]{font-family:PingFangSC-Regular;font-size:12px;color:#858585;line-height:12px;font-weight:400;white-space:nowrap}.detail-banner-new-button[data-v-51515d96]{position:relative;width:76px;margin:auto;height:28px;overflow:hidden;background:rgba(38,129,255,.1);-moz-border-radius:16px;border-radius:16px;font-size:13px;color:#2681ff;margin-top:12px}.detail-banner-new-progress[data-v-51515d96]{height:100%;background-color:rgba(0,0,0,.1);-webkit-transition:all .5s ease;-moz-transition:all .5s ease;transition:all .5s ease}.detail-banner-new-text[data-v-51515d96]{position:absolute;top:20%;left:20%;width:100%;font-size:13px;font-weight:500;line-height:28px;-webkit-transform:translate(-20%,-20%);-moz-transform:translate(-20%,-20%);-ms-transform:translate(-20%,-20%);transform:translate(-20%,-20%);text-align:center}.sweep-light-cover[data-v-51515d96]{position:absolute;width:100%;height:100%;top:0;left:150%;z-index:99;background-image:-webkit-linear-gradient(135deg,hsla(0,0%,100%,0) 32%,#fff 36%,hsla(0,0%,100%,0) 40%,hsla(0,0%,100%,0) 46%,#fff 50%,hsla(0,0%,100%,0) 54%);background-image:-moz- oldlinear-gradient(135deg,hsla(0,0%,100%,0) 32%,#fff 36%,hsla(0,0%,100%,0) 40%,hsla(0,0%,100%,0) 46%,#fff 50%,hsla(0,0%,100%,0) 54%);background-image:linear-gradient(-45deg,hsla(0,0%,100%,0) 32%,#fff 36%,hsla(0,0%,100%,0) 40%,hsla(0,0%,100%,0) 46%,#fff 50%,hsla(0,0%,100%,0) 54%);-webkit-animation:searchLights-data-v-51515d96 1s ease-in 5s infinite alternate;-moz-animation:searchLights-data-v-51515d96 1s ease-in 5s infinite alternate;animation:searchLights-data-v-51515d96 1s ease-in 5s infinite alternate}@-webkit-keyframes searchLights-data-v-51515d96{0%{left:150%}to{left:-164%}}@-moz-keyframes searchLights-data-v-51515d96{0%{left:150%}to{left:-164%}}@keyframes searchLights-data-v-51515d96{0%{left:150%}to{left:-164%}}.ripple[data-v-51515d96]{width:120px;height:120px;position:absolute;bottom:-60px;left:-60px;-moz-border-radius:50% 50%;border-radius:50% 50%;background:hsla(0,0%,100%,.4);-webkit-transform:scale(0);-moz-transform:scale(0);-ms-transform:scale(0);transform:scale(0);opacity:1}.ripple.rippleEffect[data-v-51515d96]{-webkit-animation:rippleDrop-data-v-51515d96 .6s linear;-moz-animation:rippleDrop-data-v-51515d96 .6s linear;animation:rippleDrop-data-v-51515d96 .6s linear}@-webkit-keyframes rippleDrop-data-v-51515d96{to{-webkit-transform:scale(7);transform:scale(7);opacity:0}}@-moz-keyframes rippleDrop-data-v-51515d96{to{-moz-transform:scale(7);transform:scale(7);opacity:0}}@keyframes rippleDrop-data-v-51515d96{to{-webkit-transform:scale(7);-moz-transform:scale(7);transform:scale(7);opacity:0}}
.detail-card-app[data-v-789ff6a5]{width:100%;overflow:hidden;text-align:center;position:relative;min-height:43px}.detail-card-app .logo-bar[data-v-789ff6a5]{height:20px;margin-top:16px;margin-bottom:12px}.detail-card-app .logo-bar-wrap[data-v-789ff6a5]{height:29px;width:101px;float:right;margin-right:19px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-moz-border-radius:16px;border-radius:16px;background:#f5f5f5}.detail-card-app .logo-bar-wrap .logo[data-v-789ff6a5]{height:17px;width:84px;background-color:transparent}.detail-card-app .detail-card-app-content-top[data-v-789ff6a5]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:20px 15px}.detail-card-app .detail-card-app-content-top .app-icon-wrap[data-v-789ff6a5]{-moz-border-radius:9px;border-radius:9px;display:inline-block}.detail-card-app .detail-card-app-content-top .app-icon-wrap .app-icon[data-v-789ff6a5]{display:block;width:80px;height:80px;-moz-border-radius:12px;border-radius:12px}.detail-card-app .detail-card-app-content-top .app-info[data-v-789ff6a5]{padding-left:12px;overflow:hidden;text-align:left}.detail-card-app .detail-card-app-content-top .app-info .app-title[data-v-789ff6a5]{font-size:19px;-webkit-text-stroke:.3px;line-height:1;color:#1f1f1f;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.detail-card-app .detail-card-app-content-top .app-info .description[data-v-789ff6a5]{font-size:14px;line-height:1;color:#525252;margin-top:9px;white-space:nowrap}.detail-card-app .detail-card-app-content-top .app-info .app-tag[data-v-789ff6a5]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;margin-top:9px}.detail-card-app .detail-card-app-content-top .app-info .offical[data-v-789ff6a5]{padding:4.5px 8px;line-height:1;background:rgba(38,129,255,.1);-moz-border-radius:6px;border-radius:6px;color:#2681ff;letter-spacing:0;font-size:12px;margin-right:8px}.detail-card-app .detail-card-app-content-top .app-info .safety[data-v-789ff6a5]{padding:4.5px 8px;line-height:1;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:rgba(82,82,82,.1);-moz-border-radius:6px;border-radius:6px;font-size:12px;color:#1f1f1f;font-weight:400}.detail-card-app .detail-card-app-content-top .app-info .safety-img[data-v-789ff6a5]{width:14px;height:14px;margin-right:3px;background-color:transparent}.detail-card-app .detail-card-app-content-bottom[data-v-789ff6a5]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.detail-card-app .detail-card-app-content-bottom .bottom-item[data-v-789ff6a5]{position:relative;-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.detail-card-app .detail-card-app-content-bottom .bottom-item[data-v-789ff6a5]:last-child{border-right:none}.detail-card-app .detail-card-app-content-bottom .bottom-item[data-v-789ff6a5]:after{content:"";border-left:1px solid #e0e0e0;height:27px;display:inline-block;vertical-align:middle;position:absolute;right:0;top:5px}.detail-card-app .detail-card-app-content-bottom .bottom-item[data-v-789ff6a5]:last-child:after{height:0}.detail-card-app .detail-card-app-content-bottom .score[data-v-789ff6a5]{font-size:19px;color:#1f1f1f;text-align:center;line-height:19px;font-weight:500}.detail-card-app .detail-card-app-content-bottom .desc[data-v-789ff6a5]{margin-top:6px;font-size:12px;color:#858585;text-align:center;line-height:12px;font-weight:400}.detail-card-app .detail-card-app-content-bottom .download-count[data-v-789ff6a5],.detail-card-app .detail-card-app-content-bottom .size[data-v-789ff6a5]{font-size:19px;color:#1f1f1f;line-height:19px;font-weight:500}.detail-card-app .detail-card-app-content-bottom .text[data-v-789ff6a5]{font-size:12px;line-height:12px;font-weight:400}
.detail-download-layer .has-small-margin[data-v-13c5198a]{margin-top:15px!important}.detail-download-layer .btn-wrapper[data-v-13c5198a]{margin-top:20px;padding-bottom:16px;padding-left:15px;padding-right:15px}.detail-download-layer .btn-wrapper .direct-btn[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn[data-v-13c5198a]{background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2);margin:0 auto;display:block;width:100%;line-height:18px;height:37px;padding:0;overflow:hidden;position:relative;-moz-border-radius:33px;border-radius:33px;color:#fff}.detail-download-layer .btn-wrapper .direct-btn .direct-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .direct-btn .normal-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .direct-btn .safe-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn .direct-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn .normal-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn .safe-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn .direct-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn .normal-download-progress[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn .safe-download-progress[data-v-13c5198a]{-webkit-transform:rotate(180deg);-moz-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg);position:absolute;top:0;height:100%;background:hsla(0,0%,100%,.2);-moz-border-radius:0 57px 57px 0;border-radius:0 57px 57px 0;-webkit-transition:all .5s ease;-moz-transition:all .5s ease;transition:all .5s ease}.detail-download-layer .btn-wrapper .direct-btn .direct-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .direct-btn .normal-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .direct-btn .safe-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn .direct-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn .normal-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .normal-btn .safe-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn .direct-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn .normal-text[data-v-13c5198a],.detail-download-layer .btn-wrapper .safe-btn .safe-text[data-v-13c5198a]{font-size:16px;line-height:37px;text-align:center;width:100%;height:100%;position:absolute}.detail-download-layer .btn-wrapper .btn[data-v-13c5198a]{background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2);margin:0 auto;display:block;width:100%;line-height:18px;height:37px;padding:0;overflow:hidden;font-size:15px}.detail-download-layer .btn-wrapper .btn .iconfont[data-v-13c5198a]{float:left;font-size:17.5px;position:relative;display:inline-block;vertical-align:top;top:1.2px}.detail-download-layer .btn-wrapper .btn .text[data-v-13c5198a]{display:inline-block;vertical-align:top}.detail-download-layer .btn-wrapper .btn .cBdLiteFlag[data-v-13c5198a]{display:none}.detail-download-layer .btn-wrapper .normal-down[data-v-13c5198a]{display:inline-block;margin:4px 0;color:#999;font-weight:300;font-size:15px;line-height:24px}.detail-download-layer .btn-wrapper .double-btn[data-v-13c5198a]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.detail-download-layer .btn-wrapper .double-btn-item[data-v-13c5198a]{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:18px;height:37px;padding-top:7px;padding-bottom:9px;font-size:15px;background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2)}.detail-download-layer .btn-wrapper .double-btn-item[data-v-13c5198a]:not(:first-child){margin-left:8px}.detail-download-layer .btn-wrapper .double-btn .double-btn-container[data-v-13c5198a]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;width:100%}.detail-download-layer .btn-wrapper .double-btn-explain[data-v-13c5198a]{margin-top:11px;line-height:1em;font-size:13px;color:#858585}.detail-download-layer .top-download-wrap[data-v-13c5198a]{position:absolute;width:100%;top:48px;left:0;z-index:-1;opacity:0;background:#fff;-moz-box-sizing:border-box;box-sizing:border-box;text-align:center;border-top:1px solid #f1f1f1;border-bottom:1px solid #f1f1f1}.detail-download-layer .top-download-wrap.show[data-v-13c5198a]{z-index:1;opacity:1}.detail-download-layer .top-download-wrap .normal-app-wrap[data-v-13c5198a]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;margin:0 17px;height:65px;-moz-box-sizing:border-box;box-sizing:border-box}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-img-wrap[data-v-13c5198a]{-moz-border-radius:9px;border-radius:9px;width:50px;height:50px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-img-wrap .normal-app-img[data-v-13c5198a]{width:100%;height:100%;-moz-border-radius:9px;border-radius:9px}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-content[data-v-13c5198a]{-webkit-box-flex:1;-webkit-flex:auto;-moz-box-flex:1;-ms-flex:auto;flex:auto;font-size:14px;padding:0 8px;overflow:hidden}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-content .normal-app-title[data-v-13c5198a]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;font-size:18px;font-weight:700}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-content .normal-app-info[data-v-13c5198a]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;color:#666}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-content .normal-app-info strong[data-v-13c5198a]{color:#05b4ff;font-weight:400}.detail-download-layer .top-download-wrap .normal-app-wrap .normal-app-button[data-v-13c5198a]{width:55px;height:28px;font-size:13px;background-color:#fff}.detail-download-layer .fixed-download-wrap[data-v-13c5198a]{-moz-box-shadow:0 -6px 40px 0 rgba(0,0,0,.1);box-shadow:0 -6px 40px 0 rgba(0,0,0,.1);position:fixed;width:100%;bottom:0;left:0;z-index:-1;opacity:0;background:#fff;padding:6px 15px;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-transition:all .15s;-moz-transition:all .15s;transition:all .15s;text-align:center}.detail-download-layer .fixed-download-wrap.show[data-v-13c5198a]{z-index:99;opacity:1}.detail-download-layer .fixed-download-wrap .download-info-wrap[data-v-13c5198a]{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.detail-download-layer .fixed-download-wrap .download-info-wrap .app-icon-wrap[data-v-13c5198a]{width:32px;height:32px;-moz-background-size:13px 13px;background-size:13px 13px;margin-right:13px;-moz-border-radius:6px;border-radius:6px}.detail-download-layer .fixed-download-wrap .download-info-wrap .app-icon-wrap[data-v-13c5198a]:before{-moz-border-radius:12px;border-radius:12px}.detail-download-layer .fixed-download-wrap .download-info-wrap .app-icon-wrap .app-icon[data-v-13c5198a]{width:32px;height:32px}.detail-download-layer .fixed-download-wrap .download-info-wrap .download-info h4[data-v-13c5198a]{line-height:1.3;padding-top:3px}.detail-download-layer .fixed-download-wrap .download-info-wrap .download-info p[data-v-13c5198a]{color:#666;font-size:12px;color:#1098ad;color:#15aabf}.detail-download-layer .fixed-download-wrap .btn-color[data-v-13c5198a]{background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2)}.detail-download-layer .fixed-download-wrap .fixed-btn[data-v-13c5198a]{margin:0 auto;display:block;width:100%;line-height:18px;height:40px;padding-top:7px;padding-bottom:9px;font-size:15px;background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2)}.detail-download-layer .fixed-download-wrap .fixed-direct-btn[data-v-13c5198a],.detail-download-layer .fixed-download-wrap .fixed-normal-btn[data-v-13c5198a],.detail-download-layer .fixed-download-wrap .fixed-safe-btn[data-v-13c5198a]{background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2);margin:0 auto;display:block;width:100%;line-height:18px;height:37px;padding:0;overflow:hidden;position:relative;-moz-border-radius:33px;border-radius:33px;color:#fff}.detail-download-layer .fixed-download-wrap .fixed-direct-text[data-v-13c5198a],.detail-download-layer .fixed-download-wrap .fixed-normal-text[data-v-13c5198a],.detail-download-layer .fixed-download-wrap .fixed-safe-text[data-v-13c5198a]{right:0;font-size:16px;line-height:37px;text-align:center;width:100%;height:100%;position:absolute}.detail-download-layer .fixed-download-wrap .fixed-direct-download-progress[data-v-13c5198a],.detail-download-layer .fixed-download-wrap .fixed-normal-download-progress[data-v-13c5198a],.detail-download-layer .fixed-download-wrap .fixed-safe-download-progress[data-v-13c5198a]{-webkit-transform:rotate(180deg);-moz-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg);position:absolute;top:0;height:100%;background:hsla(0,0%,100%,.2);-moz-border-radius:0 57px 57px 0;border-radius:0 57px 57px 0;-webkit-transition:all .5s ease;-moz-transition:all .5s ease;transition:all .5s ease}.detail-download-layer .fixed-download-wrap .double-fixed-btn[data-v-13c5198a]{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:18px;height:37px;padding-top:7px;padding-bottom:9px;font-size:15px;background-image:-webkit-linear-gradient(45deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(45deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(45deg,#5ca2f2,#4e6ef2)}.detail-download-layer .fixed-download-wrap .double-fixed-btn-explain[data-v-13c5198a]{margin-top:11px;line-height:1em;font-size:13px;color:#858585;text-align:left}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container{padding:21px 0 0;-moz-border-radius:21px 21px 0 0;border-radius:21px 21px 0 0}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .closer{right:18px;top:28px;color:#1f1f1f;width:14px;height:14px}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-header{font-size:19px;font-weight:500;color:#1f1f1f}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-body{color:#525252;font-size:16px;padding:21.5px 17px 20px}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-body .warning{color:#1f1f1f}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-footer{padding-right:17px;padding-left:17px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;padding-bottom:24px;text-align:center}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-footer div{padding:12px 0;display:inline-block;width:50%}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-footer .normal{height:40px;width:188px;background:#2681FF16;color:#2681ff;font-size:16px;letter-spacing:0;text-align:center;line-height:16px;font-weight:500;padding:0}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-footer .safe{width:188px;background-image:-webkit-linear-gradient(15deg,#5ca2f2,#4e6ef2);background-image:-moz- oldlinear-gradient(15deg,#5ca2f2 0,#4e6ef2 100%);background-image:linear-gradient(75deg,#5ca2f2,#4e6ef2);-moz-border-radius:22px;border-radius:22px;font-family:PingFangSC-Medium;font-size:16px;color:#fff;letter-spacing:0;text-align:center;line-height:16px;font-weight:500;height:40px;padding:0}@media screen and (max-width:400px){.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container{padding:20px 0 0}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-body{padding:10px 20px}.detail-download-layer .detail-modal[data-v-13c5198a] .modal-container .modal-footer div{padding:10px 0}}.detail-download-layer .new-download-modal .modal-wrapper .modal-container[data-v-13c5198a]{background-color:transparent;padding:0}.detail-download-layer .new-download-modal .modal-wrapper .close-wrap[data-v-13c5198a]{text-align:center}.detail-download-layer .new-download-modal .new-wrap[data-v-13c5198a]{padding:0 15px}.detail-download-layer .new-download-modal .app-name[data-v-13c5198a]{font-weight:700;margin:0 118px 0 150px;margin:0;font-size:19px;padding:21px 0 20px;line-height:1;display:inline-block;color:#1f1f1f}.detail-download-layer .new-download-modal .new-container[data-v-13c5198a]{padding-bottom:15px;background-image:url(//ascdn.baidu.com/magneton/imgs/app-bg_66827418.png);-moz-background-size:contain;background-size:contain;background-repeat:no-repeat;background-color:#fff;-moz-border-radius:20px 20px 0 0;border-radius:20px 20px 0 0}.detail-download-layer .new-download-modal .icon[data-v-13c5198a]{font-size:15px;color:#1f1f1f;position:absolute;right:15px;top:18px}.detail-download-layer .new-download-modal .app-container[data-v-13c5198a]{padding:0 15px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.detail-download-layer .new-download-modal .app-text[data-v-13c5198a]{font-size:16px;letter-spacing:0;line-height:16px;font-weight:700;padding:0 15px;margin-bottom:15px}.detail-download-layer .new-download-modal .app-tip[data-v-13c5198a]{background:#f5f6f9;-moz-border-radius:9px;border-radius:9px;margin:0 15px;font-size:13px;color:#858585;text-align:center;height:28px;line-height:28px;margin-bottom:15px}.detail-download-layer .new-download-modal .app-description[data-v-13c5198a]{width:100%}.detail-download-layer .new-download-modal .app-description-left[data-v-13c5198a]{margin-top:10px;position:absolute}.detail-download-layer .new-download-modal .app-icon[data-v-13c5198a]{width:57px;height:57px;-moz-border-radius:10px;border-radius:10px;margin-right:8px;margin-bottom:12px}.detail-download-layer .new-download-modal .app-sname[data-v-13c5198a]{line-height:1;font-size:16px;margin-bottom:9px;font-weight:700}.detail-download-layer .new-download-modal .app-info[data-v-13c5198a]{font-size:13px;color:#858585;line-height:1;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.detail-download-layer .new-download-modal .app-msg[data-v-13c5198a]{padding-right:15px}.detail-download-layer .new-download-modal .circle[data-v-13c5198a]{float:right;top:10px;position:relative;width:38px;height:38px;background:transparent;-moz-border-radius:50%;border-radius:50%}.detail-download-layer .new-download-modal .circle-icon[data-v-13c5198a]{position:absolute;width:15px;top:50%;left:50%;margin-top:-7.5px;margin-left:-7.5px}.detail-download-layer .new-download-modal .circle[data-v-13c5198a]:after{position:absolute;width:100%;height:100%;content:"";-moz-box-sizing:border-box;box-sizing:border-box;-moz-border-radius:50%;border-radius:50%;border:3px solid #eee}.detail-download-layer .new-download-modal .circle .circle-bar[data-v-13c5198a]{position:absolute;top:0;width:100%;height:100%;-moz-box-sizing:border-box;box-sizing:border-box;border:3px solid #2681ff}.detail-download-layer .new-download-modal .circle .cilcle-bar-change[data-v-13c5198a]{border:3px solid #39b362}.detail-download-layer .new-download-modal .circle-left[data-v-13c5198a]{position:absolute;z-index:1;top:0;left:0;width:50%;height:100%;overflow:hidden}.detail-download-layer .new-download-modal .circle-left .circle-bar[data-v-13c5198a]{left:100%;-moz-border-radius-topright:50px;border-top-right-radius:50px;-moz-border-radius-bottomright:50px;border-bottom-right-radius:50px;border-left:none;-webkit-transform-origin:center left;-moz-transform-origin:center left;-ms-transform-origin:center left;transform-origin:center left;-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg)}.detail-download-layer .new-download-modal .circle-right[data-v-13c5198a]{position:absolute;z-index:1;top:0;right:0;width:50%;height:100%;overflow:hidden}.detail-download-layer .new-download-modal .circle-right .circle-bar[data-v-13c5198a]{left:-100%;-moz-border-radius-topleft:50px;border-top-left-radius:50px;-moz-border-radius-bottomleft:50px;border-bottom-left-radius:50px;border-right:none;-webkit-transform-origin:center right;-moz-transform-origin:center right;-ms-transform-origin:center right;transform-origin:center right;-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg)}.detail-download-layer .new-download-modal .banner-button[data-v-13c5198a]{width:57px;height:28px;background:rgba(38,129,255,.1);-moz-border-radius:20px;border-radius:20px;position:relative;float:right;top:10px;display:table-cell;text-align:center}.detail-download-layer .new-download-modal .banner-text[data-v-13c5198a]{font-family:PingFangSC-Medium;font-size:13px;color:#2681ff;text-align:center;line-height:13px;font-weight:700;display:inline-block;padding-top:8px}@-webkit-keyframes loading-right-data-v-13c5198a{0%{-webkit-transform:rotate(0deg)}50%,to{-webkit-transform:rotate(180deg)}}@-webkit-keyframes loading-left-data-v-13c5198a{0%,50%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(180deg)}}.detail-download-layer .img-clip[data-v-13c5198a]{position:relative;width:100%;height:155px;overflow:hidden}.detail-download-layer .img-clip img[data-v-13c5198a]{width:100%;height:auto}.detail-download-layer .img-clip .shadow[data-v-13c5198a]{position:absolute;bottom:0;z-index:100;height:50px;width:100%;background-image:-webkit-gradient(linear,left top,left bottom,from(transparent),to(rgba(0,0,0,.5)));background-image:-webkit-linear-gradient(top,transparent,rgba(0,0,0,.5));background-image:-moz- oldlinear-gradient(top,transparent 0,rgba(0,0,0,.5) 100%);background-image:linear-gradient(-180deg,transparent,rgba(0,0,0,.5))}.detail-download-layer .img-clip .shadow .img-icon-container[data-v-13c5198a]{position:absolute;bottom:10px;left:7px;-moz-border-radius:50%;border-radius:50%;width:32px;height:32px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.detail-download-layer .img-clip .shadow .img-icon-container .img-icon[data-v-13c5198a]{width:100%;height:100%;-moz-border-radius:50%;border-radius:50%;background-color:transparent}.detail-download-layer .img-clip .shadow .normal-icon[data-v-13c5198a]{bottom:15px}.detail-download-layer .img-clip .shadow .play-button[data-v-13c5198a]{position:absolute;bottom:10px;right:10px;background:#05b4ff;display:inline-block;white-space:nowrap;cursor:pointer;text-align:center;-moz-box-sizing:border-box;box-sizing:border-box;padding:5px 13.5px 6px;-moz-border-radius:50px;border-radius:50px;font-size:13px;color:#fff}.detail-download-layer .img-clip .shadow .normal-button[data-v-13c5198a]{bottom:15px}.detail-download-layer .img-clip .shadow .recommend-game[data-v-13c5198a]{position:absolute;bottom:10px;left:50px;color:#fff;font-size:12px;font-weight:500}.detail-download-layer .img-clip .shadow .normal-game[data-v-13c5198a]{bottom:15px}.detail-download-layer .DevInfoBox[data-v-13c5198a]{width:274px;margin:0 auto;font-size:12px}.detail-download-layer .DevInfoBox .DevInfo[data-v-13c5198a]{text-align:left}.detail-download-layer .safety-certification[data-v-13c5198a]{width:316px;margin:0 auto;color:#999;font-size:14px;padding:0 10px 5px}.detail-download-layer .safety-certification .safety-img[data-v-13c5198a]{width:12px;height:12px}.detail-download-layer .checkbox-container[data-v-13c5198a]{width:320px;margin:0 auto;margin-top:6px;margin-left:0}.detail-download-layer .checkbox[data-v-13c5198a]:checked{background:#2681ff;border:1px solid #fff}.detail-download-layer .checkbox-color[data-v-13c5198a]:checked{background:#e0e0e0;border:1px solid #fff}.detail-download-layer .checkbox[data-v-13c5198a]{margin-top:2px!important;width:14px;height:14px;border:1px solid #b8b8b8;-moz-border-radius:50%;border-radius:50%;font-size:.8rem;margin:0;padding:0;position:relative;display:inline-block;vertical-align:top;cursor:default;-webkit-appearance:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-transition:background-color .1s ease;-moz-transition:background-color ease .1s;transition:background-color .1s ease}.detail-download-layer .checkbox[data-v-13c5198a]:checked:after{content:"";top:3px;left:3px;position:absolute;background:transparent;border:1px solid #fff;border-top:none;border-right:none;height:3px;width:5px;-moz-transform:rotate(-45deg);-ms-transform:rotate(-45deg);-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.detail-download-layer .checkboxlabel[data-v-13c5198a]{display:-webkit-box}.detail-download-layer .checkboxlabel .checkmark[data-v-13c5198a]{font-size:13px;color:#999;vertical-align:bottom;margin-left:6px;width:300px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.detail-download-layer .checkboxlabel .checkmark .securecheckmark[data-v-13c5198a]{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.detail-download-layer .checkboxlabel .checkmark .checkmarktext[data-v-13c5198a]{-webkit-flex-shrink:1;-ms-flex-negative:1;flex-shrink:1;word-wrap:break-word;text-align:left}.detail-download-layer .shouzhu-container[data-v-13c5198a]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;font-size:12px;color:#b8b8b8;font-weight:400;margin-top:4px;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;grid-column-gap:8px}.detail-download-layer .shouzhu-container .left[data-v-13c5198a]{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;grid-column-gap:8px;overflow:hidden}.detail-download-layer .shouzhu-container .left .version[data-v-13c5198a]{white-space:nowrap}.detail-download-layer .shouzhu-container .right[data-v-13c5198a]{-webkit-box-flex:0;-webkit-flex:none;-moz-box-flex:0;-ms-flex:none;flex:none;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.detail-download-layer .shouzhu-container .right .a[data-v-13c5198a]{display:inline-block}@media screen and (max-width:359px){.detail-download-layer .checkbox-container[data-v-13c5198a]{width:270px}.detail-download-layer .checkboxlabel .checkmark[data-v-13c5198a]{width:244px}}
.modal{-webkit-transition:all .3s ease;-moz-transition:all .3s ease;transition:all .3s ease;color:#323232;z-index:1000;position:fixed;top:0;bottom:0;left:0;right:0;text-align:center}.modal .modal-wrapper{width:80%;z-index:1000;position:relative;text-align:left}.modal .modal-wrapper,.modal:after{display:inline-block;vertical-align:middle}.modal:after{content:"";height:100%;width:0}.up .modal-wrapper{display:block;position:absolute;bottom:0}.modal-mask{position:fixed;z-index:999;background-color:rgba(0,0,0,.5);-webkit-transition:opacity .3s ease;-moz-transition:opacity .3s ease;transition:opacity .3s ease;margin:0;top:0;right:0;bottom:0;left:0}.modal-header{text-align:center;font-size:22px;font-weight:400}.modal-container{margin:0 auto;padding:20px;background-color:#fff;-moz-border-radius:2px;border-radius:2px;-webkit-transition:all .3s ease;-moz-transition:all .3s ease;transition:all .3s ease;position:relative}.closer{width:20px;height:20px;position:absolute;right:5px;top:5px;cursor:pointer;color:#999}.closer:hover{color:#20a0ff}.center-enter .modal-mask,.center-leave-active .modal-mask{opacity:0}.center-enter .modal-container,.center-leave-active .modal-container{-webkit-transform:translate3d(0,-20px,0);-moz-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0);opacity:0}.up-enter .modal-mask,.up-leave-active .modal-mask{opacity:0}.up-enter .modal-container,.up-leave-active .modal-container{-webkit-transform:translate3d(0,100%,0);-moz-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);opacity:0}
.detail-card-app-info[data-v-49073c5d]{padding-bottom:20px}.detail-card-app-info .app-desc[data-v-49073c5d]{padding:24px 17px 0}.detailImage .img-list li[data-v-49073c5d]{width:121px}.detail-card-app-info[data-v-49073c5d] .img-list{padding:0;overflow-x:scroll;overflow-y:hidden;white-space:nowrap}.detail-card-app-info[data-v-49073c5d] .img-list::-webkit-scrollbar{display:none}.detail-card-app-info[data-v-49073c5d] .img-list.small li{height:185.5px;width:104px}.detail-card-app-info[data-v-49073c5d] .img-list li{position:relative;width:28%;height:163px;display:inline-block;border:1px solid #fff;-moz-border-radius:9px;border-radius:9px;vertical-align:middle;margin:0 4px}.detail-card-app-info[data-v-49073c5d] .img-list li:first-child{margin-left:15px}.detail-card-app-info[data-v-49073c5d] .img-list li:last-child{margin-right:15px}.detail-card-app-info[data-v-49073c5d] .img-list li .img-wrap{overflow:hidden}.detail-card-app-info[data-v-49073c5d] .img-list li .img-wrap img{height:auto}
.c-video[data-v-01d4d69e]{height:100%;position:absolute;top:0;left:0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.game-video[data-v-01d4d69e]{width:100%}.video-wrap[data-v-01d4d69e]{overflow:hidden;-moz-border-radius:9px;border-radius:9px}.video-glass-background[data-v-01d4d69e]{width:100%;height:100%;-webkit-filter:blur(10px);filter:blur(10px)}.video-play-button[data-v-01d4d69e]{position:absolute;left:50%;top:50%;width:50px;height:50px;-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background-repeat:no-repeat;-moz-background-size:100% 100%;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJMAAACQCAMAAAA7r6mJAAAAz1BMVEUAAAAAAAD+/v4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD9/f0UFBQAAAAAAADu7u5NTU0AAAAAAAAAAAD5+fn39/fV1dUhISH09PTx8fHp6enl5eXf39/Ozs6RkZFkZGREREQ7Ozva2trExMSpqamenp56enpwcHBXV1cvLy/IyMi8vLy5ubm0tLSwsLCGhob19fWAgID///8xj+jjAAAARHRSTlNmAP1dZA1hTQhXKygGAlE8NUUmET8wIUI4FVpJF/prHgTnfFQcGfXzy2/v6+Ld1sWahXl20Lypoo6KgHLBt7SwrpTxkZo05W0AAAP6SURBVHjazNaHctpAEMbxT5x6R4DoBgzEiQtxSXHaJJnR+z9TBDbc2MGG064k/5/gN3s70kKjZS+9XndohY4wdVM4odXsxknf1ygRTC23G5rYn7DanqEpRDBJT1PgUIN2YmsKUUz9SODYnPjguOgmo+NArdC1NRm7KfAsFEhvTjQZq2nqChRtnmolmPyOACXHC5hNfmyC2sANOE2JAEfzEZupZYGrts1imsY6+DJdBtNoAN5Cg2rq6ODOTEkme4gyioLipqWArLL3A+HdKJmTYqYeSkz3CpiCLsqto2zyhyi7nqLJDlF+3UDF5M9RRU0F09RCNUVHm4Imqio+1tRGdbnHmWJUWXqMyUOl6f3DppaJahv4h0xTB1XXPGRqo/rc100JqNFXCoRl4svxXzFZIEf/HaP+l1unt14y+QJ1Zb1kilBf3n5THzUm7L2mEHUW7TOlqDXd2JneypiA6H/TAjWn21tT7Z9LWe+5aYTaM+1npiHUu7sFa/FTkwHl7i+ybHYNxkQgTYVu8JtGtu79D/A1eWJyoNoqe+jsmvviROENP8u2fboDU7ovTUUu3ka2691XMOVJU2BSTHmrU7BkSVMKoikbfwFLxs4UUU15V9/BkLczOQymbPz7BOS6W5MBikl2eQNqYmtKaCZZ4xd5VK1HU5tqks2+gZb7aBqQTbLG5xPqpxxynWgm2cU9CIkH04THJPv4gYCyN6YOtyk7/4vCLTemiNdEPGG8jcliNVFPmN7GJFhN1BNmuDb5YDVRTxhnbepzm2SrWyinB7lpwWsinzB2bkpLMMmuTqGYkZuSUk3Z+A/UauUmtxyT7FJtVKPc1CnblJ0rrfoiN8Wlm7KfUCjNTVH5phkUSv6VczYrBEVRFB4gclNkYIBEkd9kSLoD3v+dDPdMnbvW5pP1BLfu6Zy91x/0mz7w7+rSf5d/xk+7ojNuvwv0EW+Wf2fun6V3Zvrbct8Vvy3Jb/Dh2uANds8q+l7VHmTOdMtLo5kucfad182Wz2nejnC8STuCvkvpQ1OgUnZO63AZWNt3c51H7Fk5jFhWBLTsXI/OS4/snJjO3/dt3KFP5xj7OFaXHtQycdFBOumYmDj7mNx0VCZtI0hMHR2PBhRDiY6urpUFKW5CpWqKIR74NEVNe92GyGLDSNSoV48Qo1xYqFr+arvZnG8ZWn54Hr6P4Q94Q4geGqTXiOjJQnrXiB4/pBeS6BlFemuJHmSkVxvpaSd6/5EZCWSWhJi5QWaTkBkuZNYNmQkkZieRGVNkFheZWUZmu5EZeGZXALJTAdk9gezoYHaZIDtfkN04zA4hZtcSspOK2d3F7DhjdsFBO/PKugXffNBfdDCmdlW+AHOasABZFPHEAAAAAElFTkSuQmCC)}.img-list[data-v-01d4d69e]{padding:22px 0 17px;overflow-x:scroll;overflow-y:hidden;white-space:nowrap}.img-list[data-v-01d4d69e]::-webkit-scrollbar{display:none}.img-list.small li[data-v-01d4d69e]{height:185.5px;width:104px}.img-list li[data-v-01d4d69e]{position:relative;height:202px;width:113.5px;display:inline-block;border:none;margin:0 5px}.img-list li .img-wrap[data-v-01d4d69e],.img-list li .img-wrap img[data-v-01d4d69e]{width:100%;height:100%;-moz-border-radius:9px;border-radius:9px}.img-list li .img-wrap .video-img-cover[data-v-01d4d69e]{position:absolute;height:auto;left:0;top:50%;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);-moz-border-radius:0;border-radius:0}.img-list li[data-v-01d4d69e]:first-child{margin-left:19px}.img-list li[data-v-01d4d69e]:last-child{margin-right:19px}.img-list .is-show-pc[data-v-01d4d69e]{width:240px!important;height:300px!important}video[data-v-01d4d69e]{-moz-box-sizing:border-box;box-sizing:border-box;border:1px solid #fff}
.screen .swiper[data-v-6a69e081]{height:100%}.screen .swiper .swiper-item[data-v-6a69e081]{height:100%!important}.screen .swiper .item[data-v-6a69e081]{height:100%;width:100%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.screen .swiper .item .img-wrap[data-v-6a69e081]{height:90%;width:90%;background-color:#000;-moz-border-radius:0;border-radius:0;overflow:hidden;position:relative}.screen .swiper .item .img-wrap img[data-v-6a69e081]{width:100%;-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);position:absolute;top:50%;left:50%}
.full-screen{position:fixed;width:100%;height:100%;left:0;top:0;z-index:999;background-color:#000}.pop-enter-active,.pop-leave-active{-webkit-transition:all .3s;-moz-transition:all .3s;transition:all .3s}.pop-enter,.pop-leave-to{-webkit-transform:scale3d(0,0,1);-moz-transform:scale3d(0,0,1);transform:scale3d(0,0,1);opacity:0}
.swiper{overflow:hidden}.swiper,.swiper-track{width:100%;position:relative}.swiper-track{height:100%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;top:0;left:0}.swiper-indicators{position:absolute;height:0;bottom:20px;left:0;width:100%;text-align:center}.swiper-dot{display:inline-block;width:6px;height:6px;margin:3px;-moz-border-radius:100%;border-radius:100%;background:rgba(0,0,0,.5);border:1px solid hsla(0,0%,100%,.5);-webkit-transition:all .5s ease;-moz-transition:all .5s ease;transition:all .5s ease}.swiper-dot.is-active{background:#05b4ff}.swiper.vertical .swiper-track{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.swiper.vertical .swiper-indicators{left:auto;right:20px;bottom:auto;top:50%;width:8px;height:auto;-webkit-transform:translate3d(0,-50%,0);-moz-transform:translate3d(0,-50%,0);transform:translate3d(0,-50%,0)}
.swiper-item,.swiper-item-help{width:1200px;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;height:100%}
.detail-card-dev[data-v-812f927c]{padding:0 15px}.detail-card-dev .app-info .title-wrapper[data-v-812f927c]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.detail-card-dev .app-info .title-wrapper .title[data-v-812f927c]{padding-bottom:11.5px;font-size:18px;color:#333;-webkit-text-stroke:.3px;line-height:1}.detail-card-dev .app-info .update-time[data-v-812f927c]{font-size:14px;color:#858585}.detail-card-dev .app-info .tab-wrap[data-v-812f927c]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-top:11px}.detail-card-dev .app-info .tab-wrap .tabResult[data-v-812f927c],.detail-card-dev .app-info .tab-wrap i[data-v-812f927c]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex-grow:1;-moz-box-flex:1;-ms-flex-positive:1;flex-grow:1;text-align:left;color:#858585;font-size:14px}.detail-card-dev .app-info .tab-wrap .tabResult .privacy-right[data-v-812f927c],.detail-card-dev .app-info .tab-wrap i .privacy-right[data-v-812f927c]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;margin-left:20px;font-size:14px}.detail-card-dev .app-info .tab-wrap .tabResult .app-privacy[data-v-812f927c],.detail-card-dev .app-info .tab-wrap i .app-privacy[data-v-812f927c]{padding-right:20px}.detail-card-dev .app-info .tab-wrap .tab[data-v-812f927c]{color:#858585;font-size:14px}.detail-card-dev .app-info .tab-wrap-bottom[data-v-812f927c]{margin-top:8px;margin-bottom:8px}.detail-card-dev .app-info .wrapLast[data-v-812f927c]{border-bottom:none;margin-top:8px}.detail-card-dev .app-info .safety[data-v-812f927c]{margin-bottom:11px;margin-top:16.5px;color:#525252}.detail-card-dev .app-info .safety-img[data-v-812f927c],.detail-card-dev .app-info .version-img[data-v-812f927c]{width:14px;height:14px}.detail-card-dev .app-info .spread-button[data-v-812f927c]{position:absolute;right:0;top:-32px;text-align:right;color:#858585;font-size:14px}.detail-card-dev .app-info .arrow[data-v-812f927c]{margin-left:5px}.detail-card-dev .app-info .text-top[data-v-812f927c]{position:relative;font-size:14px}.detail-card-dev .app-info .spread[data-v-812f927c]{height:auto}.detail-card-dev .app-info .text[data-v-812f927c]{white-space:pre-wrap;color:#525252;line-height:27px}.detail-card-dev .app-info .hidden[data-v-812f927c]{display:none}.detail-card-dev .app-info .text-hidden[data-v-812f927c]{overflow:hidden;-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;margin-bottom:6px}
.detail-card-recommend[data-v-eea7a730]{padding-left:0;padding-right:0}.detail-card-recommend .recommend-content[data-v-eea7a730]{padding:12px 15px 0}.detail-card-recommend .recommend-app-list[data-v-eea7a730]{overflow-x:scroll;overflow-y:hidden;white-space:nowrap}.detail-card-recommend .recommend-app-list[data-v-eea7a730] .app-base-column .detail-tap{padding:10px 8px 34px 13px}.detail-card-recommend .recommend-app-list[data-v-eea7a730]::-webkit-scrollbar{display:none}.detail-card-recommend[data-v-eea7a730] .card-wrap{border:none;padding-top:26.5px;padding-bottom:0}.detail-card-recommend[data-v-eea7a730] .card-wrap .card-header{padding:0 17px}.detail-card-recommend[data-v-eea7a730] .card-wrap .card-title{line-height:1;font-weight:400;-webkit-text-stroke:.3px}.detail-card-recommend[data-v-eea7a730] .app-base-normal .normal-app-wrap .normal-app-content{padding-left:8px}.detail-card-recommend[data-v-eea7a730] .app-base-normal .normal-app-wrap .normal-app-content .normal-app-title{font-weight:400!important;-webkit-text-stroke:.3px!important;font-size:14px!important;color:#1f1f1f!important}.detail-card-recommend[data-v-eea7a730] .app-base-normal .normal-app-wrap .normal-app-content .normal-app-desc,.detail-card-recommend[data-v-eea7a730] .app-base-normal .normal-app-wrap .normal-app-content .normal-app-edition,.detail-card-recommend[data-v-eea7a730] .app-base-normal .normal-app-wrap .normal-app-content .normal-app-info{font-size:13px;color:#525252}
.card-wrap[data-v-1837f646]{border-top:5px solid #f1f1f1;padding:16px 0 7px}.card-wrap .clickable[data-v-1837f646]:active{color:#05b4ff}.card-wrap .card-header[data-v-1837f646]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:0 19px 3px}.card-wrap .card-header a[data-v-1837f646]:active{color:#05b4ff}.card-wrap .card-title[data-v-1837f646]{font-size:18px;-webkit-box-flex:1;-webkit-flex:auto;-moz-box-flex:1;-ms-flex:auto;flex:auto;font-weight:700}.card-wrap .iconfont[data-v-1837f646]{position:relative;right:-5px}
.app-base-normal[data-v-41f72878]{position:relative}.app-base-normal .detail-tap[data-v-41f72878]:active{background-color:#efeff0}.app-base-normal .detail-tap .normal-app-wrap[data-v-41f72878]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;margin:0;padding:0;height:79px;-moz-box-sizing:border-box;box-sizing:border-box}.app-base-normal .detail-tap .normal-app-wrap-index[data-v-41f72878]{margin:0 19px 0 35px}.app-base-normal .normal-app-wrap[data-v-41f72878]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;margin:0 19px;padding:11px 61px 11px 0;height:79px;-moz-box-sizing:border-box;box-sizing:border-box}.app-base-normal .normal-app-wrap-index[data-v-41f72878]{margin:0 19px 0 35px}.app-base-normal .normal-app-index[data-v-41f72878]{position:absolute;top:50%;margin-top:-12px;left:0;font-size:16px;height:24px;line-height:24px;width:34px;text-align:center;color:#c9c9c9;font-weight:300}.app-base-normal:first-child .normal-app-index[data-v-41f72878]{color:#ff132f}.app-base-normal:nth-child(2) .normal-app-index[data-v-41f72878]{color:#ff970f}.app-base-normal:nth-child(3) .normal-app-index[data-v-41f72878]{color:#ffd800}.app-base-normal .normal-app-img-wrap[data-v-41f72878]{margin-top:-13px;-moz-border-radius:12px;border-radius:12px;width:57px;height:57px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.app-base-normal .normal-app-img-wrap .normal-app-img[data-v-41f72878]{width:100%;height:100%;-moz-border-radius:12px;border-radius:12px}.app-base-normal .normal-app-content[data-v-41f72878]{-webkit-box-flex:1;-webkit-flex:auto;-moz-box-flex:1;-ms-flex:auto;flex:auto;font-size:12px;padding-left:15px;overflow:hidden;padding-bottom:11px}.app-base-normal .normal-app-content .normal-app-header[data-v-41f72878]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;margin-right:69px;margin-left:4px;grid-column-gap:8px}.app-base-normal .normal-app-content .normal-app-header .normal-app-title[data-v-41f72878]{font-size:14px;font-weight:700}.app-base-normal .normal-app-content .normal-app-info[data-v-41f72878]{color:#999;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;margin-left:4px}.app-base-normal .normal-app-content .normal-app-desc[data-v-41f72878]{margin-right:69px;margin-left:4px}.app-base-normal .normal-app-content .normal-app-download[data-v-41f72878]{margin-right:8px}.app-base-normal .normal-app-content .normal-app-bottom[data-v-41f72878]{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;width:100%;color:#525252;font-size:13px}.app-base-normal .normal-app-content .noraml-app-star[data-v-41f72878]{margin-right:5px;color:#fe9f1e}.app-base-normal .normal-app-content .normal-app-catename[data-v-41f72878]{border:1px solid hsla(0,0%,52%,.4);-moz-border-radius:4px;border-radius:4px;color:#858585}.app-base-normal .normal-app-content .normal-app-catename[data-v-41f72878],.app-base-normal .normal-app-content .normal-app-offical[data-v-41f72878]{padding-right:4px;padding-left:4px;-webkit-text-stroke:.3px;line-height:1.25;font-size:11px;vertical-align:baseline;-moz-box-sizing:content-box;box-sizing:content-box;display:inline-block;height:12px;text-decoration:none;text-overflow:ellipsis;white-space:nowrap;font-family:-apple-system,Arial,Helvetica,sans-serif;overflow:hidden}.app-base-normal .normal-app-content .normal-app-offical[data-v-41f72878]{border:1px solid rgba(38,129,255,.4);-moz-border-radius:4px;border-radius:4px;color:#2681ff;margin-right:6px}.app-base-normal .normal-app-button[data-v-41f72878]{margin-bottom:1px;position:absolute;top:50%;margin-top:-18px;right:0;padding:3.25px 16.5px 5.25px;font-size:12px;-webkit-text-stroke:.3px;background:#2681FF16;color:#2681ff}.app-base-normal .normal-app-button[data-v-41f72878]:before{border:none}.base-normal--small .normal-app-img[data-v-41f72878]{width:31px;height:31px}.base-normal--small .normal-app-content[data-v-41f72878]{padding-left:8px}.base-normal--small .normal-app-content .normal-app-info[data-v-41f72878]{font-size:10px}.base-normal--small .normal-app-content .normal-app-info .normal-app-download[data-v-41f72878]{padding-right:5px}.base-normal--middle .normal-app-img[data-v-41f72878]{width:36px;height:36px}.base-normal--middle .normal-app-content[data-v-41f72878]{padding-left:8px}.base-normal--middle .normal-app-content .normal-app-info .normal-app-download[data-v-41f72878]{padding-right:5px}.rateCompatible[data-v-41f72878]{display:block;color:#fe9f1e;-webkit-text-stroke:.3px}
.app-text{font-size:16px;letter-spacing:0;line-height:16px;font-weight:700;padding:0 15px;margin-bottom:12px}.normal-download-modal .modal-container{padding:5px 20px 0}.high-download-modal .modal-container{background-color:transparent;padding:0}
.detail-card-feedback-floating .normal-download-modal .modal-container[data-v-107672c2]{padding:5px 20px 10px;border:1px solid red}.detail-card-feedback-floating .normal-download-modal .modal-container .container .close[data-v-107672c2]{text-align:center;font-size:16px}.detail-card-feedback-floating .high-download-modal[data-v-107672c2] .modal-container{padding:20px 0 0}.detail-card-feedback-floating .high-download-modal .container[data-v-107672c2]{color:#333;position:relative}.detail-card-feedback-floating .high-download-modal .container .top-wrap[data-v-107672c2]{position:relative;top:40px;z-index:1}.detail-card-feedback-floating .high-download-modal .container .top-wrap .close-wrap[data-v-107672c2]{width:25px;height:25px;position:absolute;right:10px;top:60%;text-align:center;margin:0 auto;color:#3b4857;line-height:25px;margin-bottom:10px}.detail-card-feedback-floating .high-download-modal .container .img-wrap[data-v-107672c2]{font-size:0;width:75px;height:75px;margin:0 auto;position:relative}.detail-card-feedback-floating .high-download-modal .container .img-wrap .app-img[data-v-107672c2]{width:100%;height:100%;-moz-border-radius:9px;border-radius:9px;background-color:transparent}.detail-card-feedback-floating .high-download-modal .container .img-wrap .seal[data-v-107672c2]{position:absolute;background-color:transparent;width:55px;bottom:-15px;right:-20px;opacity:0}.detail-card-feedback-floating .high-download-modal .container .img-wrap .seal.seal-animation[data-v-107672c2]{-webkit-animation:bounce-in-data-v-107672c2 .8s ease-in;-moz-animation:bounce-in-data-v-107672c2 .8s ease-in;animation:bounce-in-data-v-107672c2 .8s ease-in;-webkit-animation-fill-mode:forwards;-moz-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-delay:1s;-moz-animation-delay:1s;animation-delay:1s}@-webkit-keyframes bounce-in-data-v-107672c2{0%{opacity:0;-webkit-transform:scale(1.5);transform:scale(1.5)}40%{opacity:1;-webkit-transform:scale(1.5);transform:scale(1.5)}60%{opacity:1;-webkit-transform:scale(1.6);transform:scale(1.6)}to{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}@-moz-keyframes bounce-in-data-v-107672c2{0%{opacity:0;-moz-transform:scale(1.5);transform:scale(1.5)}40%{opacity:1;-moz-transform:scale(1.5);transform:scale(1.5)}60%{opacity:1;-moz-transform:scale(1.6);transform:scale(1.6)}to{opacity:1;-moz-transform:scale(1);transform:scale(1)}}@keyframes bounce-in-data-v-107672c2{0%{opacity:0;-webkit-transform:scale(1.5);-moz-transform:scale(1.5);transform:scale(1.5)}40%{opacity:1;-webkit-transform:scale(1.5);-moz-transform:scale(1.5);transform:scale(1.5)}60%{opacity:1;-webkit-transform:scale(1.6);-moz-transform:scale(1.6);transform:scale(1.6)}to{opacity:1;-webkit-transform:scale(1);-moz-transform:scale(1);transform:scale(1)}}.detail-card-feedback-floating .high-download-modal .container .text-wrap[data-v-107672c2]{text-align:center;background:#fff;padding-top:62.5px;width:120%;margin-left:-10%;position:relative;overflow:hidden}.detail-card-feedback-floating .high-download-modal .container .text-wrap .bg[data-v-107672c2]{position:absolute;height:60px;width:110%;margin-left:-5%;background:hsla(0,0%,100%,.2);z-index:-1;left:0;top:0}.detail-card-feedback-floating .high-download-modal .container .text-wrap .download[data-v-107672c2]{margin:0 0 7px;font-size:18px;line-height:18px}.detail-card-feedback-floating .high-download-modal .container .text-wrap .download .blue[data-v-107672c2]{color:#34adff}.detail-card-feedback-floating .high-download-modal .container .text-wrap .open-assistant[data-v-107672c2]{font-size:15px;line-height:15px;color:#999;padding-bottom:1px}.detail-card-feedback-floating .high-download-modal .high-down-img-wrap[data-v-107672c2]{padding:15px 13px 0;background-color:#fff}.detail-card-feedback-floating .banner-wrap[data-v-107672c2]{padding-bottom:30px}.detail-card-feedback-floating .add-recommend-common[data-v-107672c2]{width:100%;height:141px}.detail-card-feedback-floating .add-recommend-common[data-v-107672c2] .card-wrap{border-top:0}.detail-card-feedback-floating .add-recommend-common[data-v-107672c2] .card-wrap .card-header{padding:7px 19px 3px 0}.detail-card-feedback-floating .add-recommend-common[data-v-107672c2] .card-wrap h2{font-size:18px;line-height:18px}.detail-card-feedback-floating .add-recommend-common[data-v-107672c2] .card-wrap i{display:none}.detail-card-feedback-floating .add-recommend-common ul[data-v-107672c2] .app-base-normal .detail-tap{padding:0 13px 30px 0}.detail-card-feedback-floating .add-recommend-common ul[data-v-107672c2] .app-base-normal .detail-tap .normal-app-wrap{margin:0}.detail-card-feedback-floating .add-recommend-common ul[data-v-107672c2] .app-base-normal .normal-app-button{right:0;top:45px}.detail-card-feedback-floating .img-clip[data-v-107672c2]{position:relative;width:100%;height:155px;overflow:hidden}.detail-card-feedback-floating .img-clip img[data-v-107672c2]{width:100%;height:auto}.detail-card-feedback-floating .img-clip .shadow[data-v-107672c2]{position:absolute;bottom:0;z-index:100;height:50px;width:100%;background-image:-webkit-gradient(linear,left top,left bottom,from(transparent),to(rgba(0,0,0,.5)));background-image:-webkit-linear-gradient(top,transparent,rgba(0,0,0,.5));background-image:-moz- oldlinear-gradient(top,transparent 0,rgba(0,0,0,.5) 100%);background-image:linear-gradient(-180deg,transparent,rgba(0,0,0,.5))}.detail-card-feedback-floating .img-clip .shadow .img-icon-container[data-v-107672c2]{position:absolute;bottom:10px;left:7px;-moz-border-radius:50%;border-radius:50%;width:32px;height:32px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.detail-card-feedback-floating .img-clip .shadow .img-icon-container .img-icon[data-v-107672c2]{width:100%;height:100%;-moz-border-radius:50%;border-radius:50%;background-color:transparent}.detail-card-feedback-floating .img-clip .shadow .normal-icon[data-v-107672c2]{bottom:15px}.detail-card-feedback-floating .img-clip .shadow .play-button[data-v-107672c2]{position:absolute;bottom:10px;right:10px;background:#05b4ff;display:inline-block;white-space:nowrap;cursor:pointer;text-align:center;-moz-box-sizing:border-box;box-sizing:border-box;padding:5px 13.5px 6px;-moz-border-radius:50px;border-radius:50px;font-size:13px;color:#fff}.detail-card-feedback-floating .img-clip .shadow .normal-button[data-v-107672c2]{bottom:15px}.detail-card-feedback-floating .img-clip .shadow .recommend-game[data-v-107672c2]{position:absolute;bottom:10px;left:50px;color:#fff;font-size:12px;font-weight:500}.detail-card-feedback-floating .img-clip .shadow .normal-game[data-v-107672c2]{bottom:15px}
.new-download-modal .modal-wrapper .modal-container{background-color:transparent;padding:0}.new-download-modal .modal-wrapper .close-wrap{text-align:center;font-weight:500;-webkit-text-stroke:.3px}
.detail-card-feedback-floating .new-download-modal .new-container[data-v-107672c2]{background-image:url(//ascdn.baidu.com/magneton/imgs/app-bg_66827418.png);-moz-background-size:contain;background-size:contain;background-repeat:no-repeat;background-color:#fff;-moz-border-radius:20px 20px 0 0;border-radius:20px 20px 0 0}.new-wrap[data-v-107672c2]{padding:0 15px}.text[data-v-107672c2]{margin:0 118px 0 150px;margin:0;font-size:19px;padding:21px 0 24px;line-height:1;display:inline-block;color:#525252}.desc-text[data-v-107672c2]{margin:0;font-size:19px;padding:0 0 24px;line-height:1;color:#525252;text-align:center}.icon[data-v-107672c2]{font-size:15px;color:#1f1f1f;position:absolute;right:15px;top:18px}</style>
		<style>
			.imageye-selected {
    outline: 2px solid black !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5) !important;
  }
</style>
	</head>

	<body style="font-size: 12px;">



		<div id="app" class="app">
			<section class="err-wrap" data-v-a010895a="" data-v-02a50d9c="">
				<div class="item-page is-show-pc" data-v-a010895a="" data-v-02a50d9c="">
					<section islandingpage="0" source="" logcategory="001" isshowpc="true" class="detail-card-app" data-v-789ff6a5="" data-v-02a50d9c="" data-v-a010895a="">
						<div class="detail-card-app-content-top" data-v-789ff6a5="">
							<div class="app-icon-wrap" data-v-789ff6a5="">
								<img alt="懂车帝" class="app-icon" data-v-789ff6a5="" src="static/logo.png" lazy="loaded">
							</div>
							<div class="app-info" data-v-789ff6a5="">
								<div class="app-title" data-v-789ff6a5="">懂车帝</div>
								<p class="description c-ellipsis" data-v-789ff6a5="">专业汽车资讯，智能购车助手</p>
								<div class="app-tag" data-v-789ff6a5="">
									<!---->
									<div class="offical" data-v-789ff6a5="">汽车服务</div>
									<!---->
								</div>
							</div>
						</div>
						<div class="detail-card-app-content-bottom" data-v-789ff6a5="">
							<div class="app-score bottom-item" data-v-789ff6a5="">
								<div class="score" data-v-789ff6a5="">4.8分</div>
								<div class="score-count desc" data-v-789ff6a5="">应用评分</div>
							</div>
							<div class="app-download-count bottom-item" data-v-789ff6a5="">
								<div class="download-count" data-v-789ff6a5="">370万</div>
								<div class="download-des desc" data-v-789ff6a5="">下载次数</div>
							</div>
							<div class="app-size bottom-item" data-v-789ff6a5="">
								<div class="size" data-v-789ff6a5="">212.15MB</div>
								<div class="size-des desc" data-v-789ff6a5="">应用大小</div>
							</div>
						</div>
					</section>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<section source="" logcategory="001" isshowpc="true" class="detail-download-layer mobile-ck" data-v-13c5198a="" data-v-02a50d9c="" data-v-a010895a="">
						<div class="top-download-wrap" data-v-13c5198a="">
							<div class="normal-app-wrap" data-v-13c5198a="">
								<div class="c-default-bg normal-app-img-wrap" data-v-13c5198a="">
									<img alt="百度手机助手" src="static/logo-single_4f4a8c66.png" class="normal-app-img" data-v-13c5198a="">
								</div>
								<div class="normal-app-content" data-v-13c5198a="">
									<h3 class="normal-app-title" data-v-13c5198a="">百度手机助手</h3>
									<div class="normal-app-info c-ellipsis" data-v-13c5198a="">
										你想要的
										<strong data-v-13c5198a="">懂车帝</strong>
										免费下载
									</div>
								</div>
								<div data-v-13c5198a="">
									<button type="button" data-agl-cvt="6" class="c-btn normal-app-button c-btn--default" data-v-eddc4c9c="" data-v-13c5198a="">
										<!---->
										<!---->
										<div class="c-ellipsis slot" data-v-eddc4c9c="">下载</div>
									</button>
									<!---->
								</div>
								<!---->
							</div>
						</div>
						<div class="btn-wrapper" data-v-13c5198a="">
							<div style="display:;" data-v-13c5198a="">
								<!---->
								<div class="double-btn" style="display:;" data-v-13c5198a="">
									<div class="double-btn-wrap" style="width:100%" data-v-13c5198a="">
										<div class="double-btn-container" data-v-13c5198a="">
											<button type="button" class="c-btn double-btn-item c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
												<!---->
												<!---->
												<div class="c-ellipsis slot" data-v-eddc4c9c="">安全下载</div>
											</button>
											<button type="button" class="c-btn double-btn-item c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
												<!---->
												<!---->
												<div class="c-ellipsis slot" data-v-eddc4c9c="">普通下载</div>
											</button>
										</div>
										<div class="double-btn-explain" data-v-13c5198a="">安全下载：使用百度手机助手为您安全下载应用</div>
										<div class="shouzhu-container" data-v-13c5198a="">
											<div class="left" data-v-13c5198a="">
												<span class="version" data-v-13c5198a="">版本*******</span>
												<div class="devname c-ellipsis" data-v-13c5198a="">
													<span data-v-13c5198a="">百度在线网络技术（北京）有限公司</span>
												</div>
											</div>
											<div class="right" data-v-13c5198a="">
												<span class="c-gap-right-middle" data-v-13c5198a="">
													功能
												</span>
												<span class="c-gap-right-middle" data-v-13c5198a="">
													隐私
												</span>
												<span data-v-13c5198a="">
													权限
												</span>
											</div>
										</div>
									</div>
								</div>
								<div class="double-btn" style="display:none;" data-v-13c5198a="">
									<div class="double-btn-wrap" style="width:100%" data-v-13c5198a="">
										<div class="double-btn-container" data-v-13c5198a="">
											<button type="button" class="c-btn double-btn-item c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
												<!---->
												<!---->
												<div class="c-ellipsis slot" data-v-eddc4c9c="">安全下载</div>
											</button>
											<button type="button" class="c-btn double-btn-item c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
												<!---->
												<!---->
												<div class="c-ellipsis slot" data-v-eddc4c9c="">普通下载</div>
											</button>
										</div>
										<div class="double-btn-explain" data-v-13c5198a="">安全下载：使用百度手机助手为您安全下载应用</div>
										<div class="shouzhu-container" data-v-13c5198a="">
											<div class="left" data-v-13c5198a="">
												<span class="c-gap-right-middle version" data-v-13c5198a="">版本*******</span>
												<div class="devname c-ellipsis" data-v-13c5198a="">
													<span data-v-13c5198a="">百度在线网络技术（北京）有限公司</span>
												</div>
											</div>
											<div class="right" data-v-13c5198a="">
												<span class="c-gap-right-middle" data-v-13c5198a="">
													功能
												</span>
												<span class="c-gap-right-middle" data-v-13c5198a="">
													隐私
												</span>
												<span data-v-13c5198a="">
													权限
												</span>
											</div>
										</div>
									</div>
								</div>
								<div style="display:none;" data-v-13c5198a="">
									<div class="safe-btn" style="display:;" data-v-13c5198a="">
										<div class="safe-download-progress" style="width:0%;" data-v-13c5198a=""></div>
										<span class="safe-text" data-v-13c5198a="">
											安全下载
										</span>
									</div>
									<div class="normal-btn" style="display:none;" data-v-13c5198a="">
										<div class="normal-download-progress" style="width:0%;" data-v-13c5198a=""></div>
										<span class="normal-text" style="display:;" data-v-13c5198a="">
											普通下载
										</span>
										<span class="normal-text" style="display:none;" data-v-13c5198a="">
											普通下载
										</span>
									</div>
									<div class="checkbox-container" data-v-13c5198a="">
										<label class="checkboxlabel" data-v-13c5198a="">
											<input type="checkbox" checked="checked" class="checkbox" data-v-13c5198a="">
											<span class="checkmark" data-v-13c5198a="">
												<span class="securecheckmark" data-v-13c5198a="">安全下载：</span>
												<span class="checkmarktext" data-v-13c5198a="">通过百度手机助手为您安全下载应用</span>
											</span>
										</label>
									</div>
									<div class="shouzhu-container" data-v-13c5198a="">
										<div class="left" data-v-13c5198a="">
											<span class="c-gap-right-middle version" data-v-13c5198a="">版本*******</span>
											<div class="devname c-ellipsis" data-v-13c5198a="">
												<span data-v-13c5198a="">百度在线网络技术（北京）有限公司</span>
											</div>
										</div>
										<div class="right" data-v-13c5198a="">
											<span class="c-gap-right-middle" data-v-13c5198a="">
												功能
											</span>
											<span class="c-gap-right-middle" data-v-13c5198a="">
												隐私
											</span>
											<span data-v-13c5198a="">
												权限
											</span>
										</div>
									</div>
								</div>
							</div>
							<div class="direct-btn" style="display:none;" data-v-13c5198a="">
								<div class="direct-download-progress" style="width:0%;" data-v-13c5198a=""></div>
								<span class="direct-text" data-v-13c5198a="">普通下载</span>
							</div>
							<div style="display:none;" data-v-13c5198a="">
								<button type="button" class="c-btn btn c-btn--primary-orange" data-v-eddc4c9c="" data-v-13c5198a="">
									<!---->
									<!---->
									<div class="c-ellipsis slot" data-v-eddc4c9c="">立即更新</div>
								</button>
							</div>
						</div>
						<div class="fixed-download-wrap" data-v-13c5198a="">
							<div style="display:;" data-v-13c5198a="">
								<!---->
								<div style="display:none;" data-v-13c5198a="">
									<div class="fixed-safe-btn" style="display:;" data-v-13c5198a="">
										<div class="fixed-safe-download-progress" style="width:0%;" data-v-13c5198a=""></div>
										<span class="fixed-safe-text" data-v-13c5198a="">安全下载 懂车帝</span>
									</div>
									<div class="fixed-normal-btn" style="display:none;" data-v-13c5198a="">
										<div class="fixed-normal-download-progress" style="width:0%;" data-v-13c5198a=""></div>
										<span class="fixed-normal-text" data-v-13c5198a="">普通下载 懂车帝</span>
									</div>
									<div class="fixed-normal-btn" style="display:none;" data-v-13c5198a="">
										<div class="fixed-normal-download-progress" style="width:0%;" data-v-13c5198a=""></div>
										<span class="fixed-normal-text" data-v-13c5198a="">普通下载 懂车帝</span>
									</div>
									<div class="checkbox-container" data-v-13c5198a="">
										<label class="checkboxlabel" data-v-13c5198a="">
											<input type="checkbox" checked="checked" class="checkbox" data-v-13c5198a="">
											<span class="checkmark" data-v-13c5198a="">
												<span class="securecheckmark" data-v-13c5198a="">安全下载：</span>
												<span class="checkmarktext" data-v-13c5198a="">通过百度手机助手为您安全下载应用</span>
											</span>
										</label>
									</div>
									<div class="shouzhu-container" data-v-13c5198a="">
										<div class="left" data-v-13c5198a="">
											<span class="c-gap-right-middle version" data-v-13c5198a="">版本*******</span>
											<div class="devname c-ellipsis" data-v-13c5198a="">
												<span data-v-13c5198a="">百度在线网络技术（北京）有限公司</span>
											</div>
										</div>
										<div class="right" data-v-13c5198a="">
											<span class="c-gap-right-middle" data-v-13c5198a="">
												功能
											</span>
											<span class="c-gap-right-middle" data-v-13c5198a="">
												隐私
											</span>
											<span data-v-13c5198a="">
												权限
											</span>
										</div>
									</div>
								</div>
								<div style="display:;" data-v-13c5198a="">
									<div style="display: flex" data-v-13c5198a="">
										<button type="button" class="c-btn double-fixed-btn c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
											<!---->
											<!---->
											<div class="c-ellipsis slot" data-v-eddc4c9c="">安全下载</div>
										</button>
										<button type="button" class="c-btn double-fixed-btn c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
											<!---->
											<!---->
											<div class="c-ellipsis slot" data-v-eddc4c9c="">普通下载</div>
										</button>
									</div>
									<div class="double-fixed-btn-explain" data-v-13c5198a="">安全下载：使用百度手机助手为您安全下载应用</div>
									<div class="shouzhu-container" data-v-13c5198a="">
										<div class="left" data-v-13c5198a="">
											<span class="c-gap-right-middle version" data-v-13c5198a="">版本*******</span>
											<div class="devname c-ellipsis" data-v-13c5198a="">
												<span data-v-13c5198a="">百度在线网络技术（北京）有限公司</span>
											</div>
										</div>
										<div class="right" data-v-13c5198a="">
											<span class="c-gap-right-middle" data-v-13c5198a="">
												功能
											</span>
											<span class="c-gap-right-middle" data-v-13c5198a="">
												隐私
											</span>
											<span data-v-13c5198a="">
												权限
											</span>
										</div>
									</div>
								</div>
								<div style="display:none;" data-v-13c5198a="">
									<div style="display: flex" data-v-13c5198a="">
										<button type="button" class="c-btn double-fixed-btn c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
											<!---->
											<!---->
											<div class="c-ellipsis slot" data-v-eddc4c9c="">安全下载</div>
										</button>
										<button type="button" class="c-btn double-fixed-btn c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
											<!---->
											<!---->
											<div class="c-ellipsis slot" data-v-eddc4c9c="">普通下载</div>
										</button>
									</div>
									<div class="double-fixed-btn-explain" data-v-13c5198a="">安全下载：使用百度手机助手为您安全下载应用</div>
									<div class="shouzhu-container" data-v-13c5198a="">
										<div class="left" data-v-13c5198a="">
											<span class="c-gap-right-middle version" data-v-13c5198a="">版本*******</span>
											<div class="devname c-ellipsis" data-v-13c5198a="">
												<span data-v-13c5198a="">百度在线网络技术（北京）有限公司</span>
											</div>
										</div>
										<div class="right" data-v-13c5198a="">
											<span class="c-gap-right-middle" data-v-13c5198a="">
												功能
											</span>
											<span class="c-gap-right-middle" data-v-13c5198a="">
												隐私
											</span>
											<span data-v-13c5198a="">
												权限
											</span>
										</div>
									</div>
								</div>
							</div>
							<div class="fixed-direct-btn" style="display:none;" data-v-13c5198a="">
								<div class="fixed-direct-download-progress" style="width:0%;" data-v-13c5198a=""></div>
								<span class="fixed-direct-text" data-v-13c5198a="">普通下载</span>
							</div>
							<div style="display:none;" data-v-13c5198a="">
								<button type="button" class="c-btn fixed-btn c-btn--primary-orange" data-v-eddc4c9c="" data-v-13c5198a="">
									<!---->
									<!---->
									<div class="c-ellipsis slot" data-v-eddc4c9c="">立即更新</div>
								</button>
							</div>
						</div>
						<!---->
						<div class="modal detail-modal up" style="display:none;" data-v-13c5198a="">
							<div class="modal-wrapper" style="width:100%;">
								<div class="modal-container">
									<div class="modal-closer">
										<div class="closer iconfont el-icon-close"></div>
									</div>
									<div class="modal-header">安全建议</div>
									<div class="modal-body">
										建议使用百度手机助手安全下载，<span class="warning" data-v-13c5198a="">安全下载能够为您在下载时提供数据检测和加速等服务。</span>
									</div>
									<div class="modal-footer">
										<button type="button" class="c-btn safe c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
											<!---->
											<!---->
											<div class="c-ellipsis slot" data-v-eddc4c9c="">安全下载</div>
										</button>
										<button type="button" class="c-btn normal c-btn--primary" data-v-eddc4c9c="" data-v-13c5198a="">
											<!---->
											<!---->
											<div class="c-ellipsis slot" data-v-eddc4c9c="">普通下载</div>
										</button>
									</div>
								</div>
							</div>
							<div class="modal-mask"></div>
						</div>
						<!---->
					</section>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<section bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="detail-card-app-info" data-v-49073c5d="" data-v-02a50d9c="" data-v-a010895a="">
						<ul class="img-list detailImage" data-v-01d4d69e="" data-v-49073c5d="">
							<!---->
							<li data-v-01d4d69e="" class="is-show-pc">
								<div class="img-wrap" data-v-01d4d69e="">
									<img alt="" data-v-01d4d69e="" src="static/1.png" lazy="loaded">
								</div>
							</li>
							<li data-v-01d4d69e="" class="is-show-pc">
								<div class="img-wrap" data-v-01d4d69e="">
									<img alt="" data-v-01d4d69e="" src="static/2.png" lazy="loaded">
								</div>
							</li>
						</ul>
						<section class="detail-img-big-list" data-v-6a69e081="" data-v-49073c5d="">
							<section class="full-screen screen" style="display: none;" data-v-6a69e081="">
								<div class="close-btn" onclick="hideFullScreen()" style="position: fixed; top: 20px; right: 20px; z-index: 10000; width: 40px; height: 40px; background: rgba(0,0,0,0.5); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: white; font-size: 24px;">×</div>
								<div class="swiper swiper" data-v-6a69e081="">
									<div class="swiper-track" style="transform: translate3d(0px, 0px, 0px); transition-duration: 0ms;">
										<!---->
										<!---->
										<div class="swiper-item swiper-item is-active" style="width: 375px; height: auto;" data-v-6a69e081="">
											<div class="item" data-v-6a69e081="">
												<div class="c-default-bg img-wrap" data-v-6a69e081="">
													<img alt="" style="width:100%;height:auto;" data-v-6a69e081="" src="static/1.png" lazy="loaded">
												</div>
											</div>
										</div>
										<div class="swiper-item swiper-item" style="width: 375px; height: auto;" data-v-6a69e081="">
											<div class="item" data-v-6a69e081="">
												<div class="c-default-bg img-wrap" data-v-6a69e081="">
													<img alt="" style="width:100%;height:auto;" data-v-6a69e081="" src="static/2.png" lazy="loaded">
												</div>
											</div>
										</div>
										<!---->
										<!---->
									</div>
									<div class="swiper-indicators">
										<div class="swiper-dot is-active"></div>
										<div class="swiper-dot"></div>
									</div>
								</div>
							</section>
						</section>
					</section>
					<section bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="detail-card-dev" data-v-812f927c="" data-v-02a50d9c="" data-v-a010895a="">
						<div class="app-info" data-v-812f927c="">
							<div class="title-wrapper" data-v-812f927c="">
								<div class="title" data-v-812f927c="">功能简介</div>
							</div>
							<div class="text-top" data-v-812f927c="">
								<div class="text-wrap" data-v-812f927c="">
									<p class="text" data-v-812f927c="">懂车帝APP是一款专业的汽车资讯和购车服务平台，为用户提供全面的汽车信息、专业的购车指导和丰富的汽车内容。基于强大的数据分析和专业的汽车团队，懂车帝致力于为用户提供最权威、最实用的汽车服务，主要功能包括：
										- 汽车资讯和评测
										- 购车指导和对比
										- 汽车视频内容
										- 车型配置查询
										- 价格趋势分析
										- 用户口碑评价
										- 经销商信息查询
										- 汽车金融服务
										- 专业测试报告
										- 新车上市资讯
										- 汽车导购建议
										- 车主社区交流
										- 更多汽车服务等你来体验...

										「懂车帝」汇聚了专业的汽车编辑团队和丰富的汽车内容，为用户提供从选车、买车到用车的全方位服务。平台拥有海量的汽车数据和专业的测评内容，已成为千万用户信赖的汽车服务平台。

										「专业服务」懂车帝与众多汽车厂商和经销商建立合作关系，为用户提供最新的汽车资讯、最优惠的购车方案和最专业的汽车知识。

										「智能推荐」基于用户需求和偏好，懂车帝为每位用户提供个性化的汽车推荐和购车建议，让选车买车更简单！</p>
									<!---->
									<div class="update-time" data-v-812f927c="">
										<span data-v-812f927c="">更新时间：</span>
										<span data-v-812f927c="">2025-07-29 09:19</span>
									</div>
									<div class="tab-wrap wrapLast" data-v-812f927c="">
										<span class="tab" data-v-812f927c="">版本：</span>
										<span class="tabResult" data-v-812f927c="">
											5.43.0
											<span class="privacy-right" data-v-812f927c="">
												<span class="app-privacy" data-v-812f927c="">
													隐私政策
												</span>
												<span data-v-812f927c="" class="">
													应用权限
												</span>
											</span>
										</span>
									</div>
									<div class="tab-wrap tab-wrap-bottom" data-v-812f927c="">
										<span class="tab" data-v-812f927c="">备案号：</span>
										<span class="tabResult" data-v-812f927c="">粤ICP备16004731号-6A</span>
									</div>
									<div class="tab-wrap tab-wrap-bottom" data-v-812f927c="">
										<span class="tab" data-v-812f927c="">开发者：</span>
										<span class="tabResult" data-v-812f927c="">广州前实网络科技有限公司</span>
									</div>
								</div>
								<div class="spread-button" data-v-812f927c="">
									收起
									<i class="iconfont arrow el-icon-arrow-up" data-v-812f927c=""></i>
								</div>
							</div>
						</div>
						<br>
						<br>
						<br>
						<br>
						
					</section>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div bdliteflag="0" islandingpage="0" source="" isshowpc="true" class="detail-card-recommend" data-v-eea7a730="" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<div card-data="[object Object]" bdliteflag="0" islandingpage="0" source="" logcategory="001" isshowpc="true" class="none" data-v-02a50d9c="" data-v-a010895a=""></div>
					<section islandingpage="0" source="" isshowpc="true" class="detail-card-feedback-floating" data-v-107672c2="" data-v-02a50d9c="" data-v-a010895a="">
						<div class="modal new-download-modal up" style="display:none;" data-v-107672c2="">
							<div class="modal-wrapper" style="width:100%;">
								<div class="modal-container">
									<!---->
									<div class="modal-header"></div>
									<div class="modal-body">
										<div class="new-container" data-v-107672c2="">
											<div class="new-top-wrap" data-v-107672c2="">
												<div class="close-wrap" data-v-107672c2="">
													<div class="text" data-v-107672c2="">懂车帝已启动下载</div>
													<i class="iconfont el-icon-close icon" data-v-107672c2=""></i>
												</div>
												<div class="desc-text" data-v-107672c2="">继续为您推荐精彩应用 - 百度极速版</div>
											</div>
											<div class="new-wrap" data-v-107672c2="">
												<div class="img-clip" data-v-107672c2="">
													<img src="static/a368eb0f243ec1da13874f44827191d8.jpg" alt="百度手机助手" class="high-down-img" data-v-107672c2="">
												</div>
											</div>
										</div>
									</div>
									<div class="modal-footer"></div>
								</div>
							</div>
							<div class="modal-mask"></div>
						</div>
						<div class="modal new-download-modal up" style="display:none;" data-v-107672c2="">
							<div class="modal-wrapper" style="width:100%;">
								<div class="modal-container">
									<!---->
									<div class="modal-header"></div>
									<div class="modal-body">
										<div class="new-container" data-v-107672c2="">
											<div class="new-top-wrap" data-v-107672c2="">
												<div class="close-wrap" data-v-107672c2="">
													<div class="text" data-v-107672c2="">懂车帝已启动下载</div>
													<i class="iconfont el-icon-close icon" data-v-107672c2=""></i>
												</div>
												<div class="desc-text" data-v-107672c2="">继续为您推荐精彩应用 - 百度极速版</div>
											</div>
											<div class="new-wrap" data-v-107672c2="">
												<div class="img-clip" data-v-107672c2="">
													<img src="static/a368eb0f243ec1da13874f44827191d8.jpg" alt="百度手机助手" class="high-down-img" data-v-107672c2="">
												</div>
											</div>
										</div>
									</div>
									<div class="modal-footer"></div>
								</div>
							</div>
							<div class="modal-mask"></div>
						</div>
					</section>
				</div>
			</section>
		</div>


	<script>
		// 动态加载下载链接
		let downloadUrl = ''; // 完全依赖txt文件

		// 尝试读取txt文件中的下载链接
		function loadDownloadUrl() {
			try {
				fetch('appdown.txt')
					.then(response => {
						if (response.ok) {
							return response.text();
						}
						throw new Error('Failed to load download URL');
					})
					.then(data => {
						downloadUrl = data.trim();
						console.log('Download URL loaded:', downloadUrl);
					})
					.catch(error => {
						console.warn('Could not load download URL from file, using default:', error);
					});
			} catch (error) {
				console.warn('Fetch not available, using default download URL');
			}
		}

		// 下载按钮点击事件
		function handleDownload() {
			if (downloadUrl) {
				window.open(downloadUrl, '_blank');
			}
		}

		// 全局函数：隐藏全屏轮播
		function hideFullScreen() {
			const fullScreenSection = document.querySelector('.full-screen.screen');
			if (fullScreenSection) {
				fullScreenSection.style.display = 'none';
				document.body.style.overflow = 'auto';
			}
		}

		// 全局函数：显示全屏轮播
		function showFullScreen() {
			const fullScreenSection = document.querySelector('.full-screen.screen');
			if (fullScreenSection) {
				fullScreenSection.style.display = 'block';
				document.body.style.overflow = 'hidden';
			}
		}

		// 键盘事件支持
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape') {
				hideFullScreen();
			}
		});

		// 为所有下载按钮添加点击事件
		document.addEventListener('DOMContentLoaded', function() {
			// 安全下载按钮
			const safeDownloadBtns = document.querySelectorAll('.double-btn-item:first-child, .safe-btn, .fixed-safe-btn');
			safeDownloadBtns.forEach(btn => {
				btn.addEventListener('click', handleDownload);
			});

			// 普通下载按钮
			const normalDownloadBtns = document.querySelectorAll('.double-btn-item:last-child, .normal-btn, .fixed-normal-btn, .direct-btn, .fixed-direct-btn');
			normalDownloadBtns.forEach(btn => {
				btn.addEventListener('click', handleDownload);
			});

			// 图片轮播点击放大功能
			const imageList = document.querySelector('.img-list.detailImage');
			const fullScreenSection = document.querySelector('.full-screen.screen');
			const swiper = document.querySelector('.swiper');
			const swiperTrack = document.querySelector('.swiper-track');
			const swiperItems = document.querySelectorAll('.swiper-item');
			const swiperDots = document.querySelectorAll('.swiper-dot');

			let currentIndex = 0;
			let startX = 0;
			let isDragging = false;

			// 图片点击事件
			if (imageList) {
				const images = imageList.querySelectorAll('li');
				images.forEach((img, index) => {
					img.addEventListener('click', function() {
						currentIndex = index;
						showFullScreen();
						updateSwiper();
					});
				});
			}



			// 更新轮播位置
			function updateSwiper() {
				if (swiperTrack && swiperItems.length > 0) {
					const itemWidth = swiperItems[0].offsetWidth;
					swiperTrack.style.transform = `translate3d(-${currentIndex * itemWidth}px, 0px, 0px)`;
					swiperTrack.style.transitionDuration = '300ms';

					// 更新指示器
					swiperDots.forEach((dot, index) => {
						if (index === currentIndex) {
							dot.classList.add('is-active');
						} else {
							dot.classList.remove('is-active');
						}
					});
				}
			}

			// 点击空白区域关闭
			if (fullScreenSection) {
				fullScreenSection.addEventListener('click', function(e) {
					if (e.target === fullScreenSection || e.target.classList.contains('screen')) {
						hideFullScreen();
					}
				});
			}

			// 触摸滑动支持
			if (swiper) {
				swiper.addEventListener('touchstart', function(e) {
					startX = e.touches[0].clientX;
					isDragging = true;
					swiperTrack.style.transitionDuration = '0ms';
				});

				swiper.addEventListener('touchmove', function(e) {
					if (!isDragging) return;
					e.preventDefault();
					const currentX = e.touches[0].clientX;
					const diffX = startX - currentX;
					const itemWidth = swiperItems[0].offsetWidth;
					const translateX = -currentIndex * itemWidth - diffX;
					swiperTrack.style.transform = `translate3d(${translateX}px, 0px, 0px)`;
				});

				swiper.addEventListener('touchend', function(e) {
					if (!isDragging) return;
					isDragging = false;
					const endX = e.changedTouches[0].clientX;
					const diffX = startX - endX;

					if (Math.abs(diffX) > 50) {
						if (diffX > 0 && currentIndex < swiperItems.length - 1) {
							currentIndex++;
						} else if (diffX < 0 && currentIndex > 0) {
							currentIndex--;
						}
					}
					updateSwiper();
				});
			}

			// 指示器点击事件
			swiperDots.forEach((dot, index) => {
				dot.addEventListener('click', function() {
					currentIndex = index;
					updateSwiper();
				});
			});

			// 功能简介展开/收起功能
			const spreadButton = document.querySelector('.spread-button');
			const textWrap = document.querySelector('.text-wrap');

			if (spreadButton && textWrap) {
				let isExpanded = false; // 默认收起状态

				// 获取文本内容的实际高度
				const getTextHeight = () => {
					const clone = textWrap.cloneNode(true);
					clone.style.maxHeight = 'none';
					clone.style.position = 'absolute';
					clone.style.visibility = 'hidden';
					clone.style.width = textWrap.offsetWidth + 'px';
					document.body.appendChild(clone);
					const height = clone.offsetHeight;
					document.body.removeChild(clone);
					return height;
				};

				const fullHeight = getTextHeight();
				const collapsedHeight = 300; // 收起时的高度限制

				// 初始化状态
				function initTextState() {
					if (isExpanded) {
						// 展开：显示完整内容
						textWrap.style.maxHeight = 'none';
						textWrap.style.overflow = 'visible';
						spreadButton.innerHTML = '展开 <i class="iconfont arrow el-icon-arrow-down"></i>';
					} else {
						// 收起：限制高度
						textWrap.style.maxHeight = collapsedHeight + 'px';
						textWrap.style.overflow = 'hidden';
						spreadButton.innerHTML = '展开 <i class="iconfont arrow el-icon-arrow-down"></i>';
					}
				}

				// 只有当内容高度超过限制时才显示按钮
				if (fullHeight > collapsedHeight) {
					initTextState();

					spreadButton.addEventListener('click', function() {
						isExpanded = !isExpanded;

						if (isExpanded) {
							// 展开：显示完整内容
							textWrap.style.maxHeight = 'none';
							textWrap.style.overflow = 'visible';
							spreadButton.innerHTML = '收起 <i class="iconfont arrow el-icon-arrow-up"></i>';
						} else {
							// 收起：限制高度
							textWrap.style.maxHeight = collapsedHeight + 'px';
							textWrap.style.overflow = 'hidden';
							spreadButton.innerHTML = '展开 <i class="iconfont arrow el-icon-arrow-down"></i>';
						}
					});
				} else {
					// 如果内容不够长，隐藏展开/收起按钮
					spreadButton.style.display = 'none';
					textWrap.style.maxHeight = 'none';
					textWrap.style.overflow = 'visible';
				}
			}



			// 图片加载错误处理
			const allImages = document.querySelectorAll('img');
			allImages.forEach(img => {
				img.addEventListener('error', function() {
					this.style.backgroundColor = '#f0f0f0';
					this.style.display = 'flex';
					this.style.alignItems = 'center';
					this.style.justifyContent = 'center';
					this.alt = '图片加载失败';
				});
			});

			// 滚动监听 - 控制fixed-download-wrap显示/隐藏
			const fixedDownloadWrap = document.querySelector('.fixed-download-wrap');
			let lastScrollTop = 0;
			let isFixedDownloadVisible = false;
			let scrollUpDistance = 0; // 累计向上滚动距离

			if (fixedDownloadWrap) {
				window.addEventListener('scroll', function() {
					const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
					const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
					const scrollPercentage = (scrollTop / documentHeight) * 100;

					// 当滚动到75%时显示
					if (scrollPercentage >= 75 && !isFixedDownloadVisible) {
						fixedDownloadWrap.classList.add('show');
						isFixedDownloadVisible = true;
						scrollUpDistance = 0; // 重置向上滚动距离
					}

					// 检测滚动方向
					if (scrollTop < lastScrollTop) {
						// 向上滚动
						scrollUpDistance += (lastScrollTop - scrollTop);

						// 累计向上滚动超过80px时隐藏
						if (isFixedDownloadVisible && scrollUpDistance > 80) {
							fixedDownloadWrap.classList.remove('show');
							isFixedDownloadVisible = false;
							scrollUpDistance = 0;
						}
					} else if (scrollTop > lastScrollTop) {
						// 向下滚动时重置向上滚动距离
						scrollUpDistance = 0;
					}

					// 如果滚动到页面顶部附近（小于20%），强制隐藏
					if (isFixedDownloadVisible && scrollPercentage < 20) {
						fixedDownloadWrap.classList.remove('show');
						isFixedDownloadVisible = false;
						scrollUpDistance = 0;
					}

					lastScrollTop = scrollTop;
				});

				// 为fixed-download-wrap中的下载按钮添加点击事件
				const fixedDownloadBtns = fixedDownloadWrap.querySelectorAll('.fixed-safe-btn, .fixed-normal-btn, .fixed-direct-btn, .double-fixed-btn');
				fixedDownloadBtns.forEach(btn => {
					btn.addEventListener('click', handleDownload);
				});
			}

			// 加载下载链接
			loadDownloadUrl();
		});
	</script>
</body>
</html>